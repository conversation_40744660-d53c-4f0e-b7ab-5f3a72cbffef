
/***** 工具函数 Start *****/
/**
 * @description 判断是否为QQ环境内
 * @returns {boolean}
 */
function isQQ() {
    var u = window.navigator.userAgent;
    const ret = (u.toLowerCase().indexOf('mqqbrowser') > -1 && u.toLowerCase().indexOf(" qq") > -1) ||
      (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) && u.toLowerCase().indexOf(" qq") > -1)
    return ret;
}

/**
 * @description 判断是否是微信环境
 * @returns {boolean}
 */
function isWechat() {
    var ua = navigator.userAgent.toLowerCase()
    var isWXWork = ua.match(/wxwork/i) == 'wxwork'
    var isWeixin = !isWXWork && ua.match(/MicroMessenger/i) == 'micromessenger'
    return isWeixin;
}

//判断ios
function isIOS() {
    var u = navigator.userAgent;
    var result = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    return result;
}

// 对象转化为url参数
function paramsEnUrl(params) {
    var urlParam = '';
    for (var key in params) {
        if (urlParam === '') {
            urlParam = urlParam + key + '=' + params[key]
        } else {
            urlParam = urlParam + '&' + key + '=' + params[key]
        }
    }
    return urlParam;
}

// 获取url参数
function getQueryString(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return pair[1];
        }
    }
    return ('');
}

/***** 工具函数 End *****/

/***** 弹窗  *****/
function TGDialogS(e, notCloseOther){
    if(!notCloseOther) lay.closeAll()
    lay.open(e, {time: 0})
    // 利用milo库引入dialog组件
    // need("biz.dialog",function(Dialog){
    //     Dialog.show({
    //         id:e,
    //         bgcolor:'#000', //弹出“遮罩”的颜色，格式为"#FF6600"，可修改，默认为"#fff"
    //         opacity:50 //弹出“遮罩”的透明度，格式为｛10-100｝，可选
    //     });
    // });
    document.body.style.overflow = 'hidden';
}
function closeDialog(e){
    if(e) {
        lay.close(e)
    } else{
        lay.closeAll()
    }
    // // 利用milo库引入dialog组件
    // need("biz.dialog",function(Dialog){
    //     Dialog.hide();
    // });
    document.body.style.overflow = 'auto';
}

/**
 * 通用错误提示弹窗
 * @param c 提示文案内容
 */
function showAlert(c) {
    $('#popAlert .J_alert_msg').html(c)
    TGDialogS('popAlert')
}


// 进入指定页面: index-首页  main-其他页
function go(pageId){
    pageId = pageId ? pageId : 'index'
    const page_classname = '.page_' + pageId
    console.log(page_classname);
    $(page_classname).addClass('show').siblings().removeClass('show')
}

