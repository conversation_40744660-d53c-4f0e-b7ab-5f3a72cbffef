
/***** 宸ュ叿鍑芥暟 Start *****/
/**
 * @description 鍒ゆ柇鏄惁涓篞Q鐜鍐�
 * @returns {boolean}
 */
function isQQ() {
    var u = window.navigator.userAgent;
    const ret = (u.toLowerCase().indexOf('mqqbrowser') > -1 && u.toLowerCase().indexOf(" qq") > -1) ||
      (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) && u.toLowerCase().indexOf(" qq") > -1)
    return ret;
}

/**
 * @description 鍒ゆ柇鏄惁鏄井淇＄幆澧�
 * @returns {boolean}
 */
function isWechat() {
    var ua = navigator.userAgent.toLowerCase()
    var isWXWork = ua.match(/wxwork/i) == 'wxwork'
    var isWeixin = !isWXWork && ua.match(/MicroMessenger/i) == 'micromessenger'
    return isWeixin;
}

//鍒ゆ柇ios
function isIOS() {
    var u = navigator.userAgent;
    var result = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    return result;
}

// 瀵硅薄杞寲涓簎rl鍙傛暟
function paramsEnUrl(params) {
    var urlParam = '';
    for (var key in params) {
        if (urlParam === '') {
            urlParam = urlParam + key + '=' + params[key]
        } else {
            urlParam = urlParam + '&' + key + '=' + params[key]
        }
    }
    return urlParam;
}

// 鑾峰彇url鍙傛暟
function getQueryString(variable) {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
            return pair[1];
        }
    }
    return ('');
}

/***** 宸ュ叿鍑芥暟 End *****/

/***** 寮圭獥  *****/
function TGDialogS(e, notCloseOther){
    if(!notCloseOther) lay.closeAll()
    lay.open(e, {time: 0})
    // 鍒╃敤milo搴撳紩鍏ialog缁勪欢
    // need("biz.dialog",function(Dialog){
    //     Dialog.show({
    //         id:e,
    //         bgcolor:'#000', //寮瑰嚭鈥滈伄缃┾€濈殑棰滆壊锛屾牸寮忎负"#FF6600"锛屽彲淇敼锛岄粯璁や负"#fff"
    //         opacity:50 //寮瑰嚭鈥滈伄缃┾€濈殑閫忔槑搴︼紝鏍煎紡涓猴經10-100锝濓紝鍙€�
    //     });
    // });
    document.body.style.overflow = 'hidden';
}
function closeDialog(e){
    if(e) {
        lay.close(e)
    } else{
        lay.closeAll()
    }
    // // 鍒╃敤milo搴撳紩鍏ialog缁勪欢
    // need("biz.dialog",function(Dialog){
    //     Dialog.hide();
    // });
    document.body.style.overflow = 'auto';
}

/**
 * 閫氱敤閿欒鎻愮ず寮圭獥
 * @param c 鎻愮ず鏂囨鍐呭
 */
function showAlert(c) {
    $('#popAlert .J_alert_msg').html(c)
    TGDialogS('popAlert')
}


// 杩涘叆鎸囧畾椤甸潰: index-棣栭〉  main-鍏朵粬椤�
function go(pageId){
    pageId = pageId ? pageId : 'index'
    const page_classname = '.page_' + pageId
    console.log(page_classname);
    $(page_classname).addClass('show').siblings().removeClass('show')
}

