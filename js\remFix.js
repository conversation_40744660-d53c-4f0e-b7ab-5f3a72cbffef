~(function () {
    var html = document.documentElement;
    var body = document.body;
    var d = document.createElement('div');
    d.style.cssText="width:1rem; overflow: hidden;position:absolute;z-index:-1;visibility: hidden;";
    body.appendChild(d);
    var dw = d.offsetWidth; // 1rem表示的实际字号（大部分标准字号是16px）
    var fz = 100; //正常计算出来的rem基准值 , 可自行修改为rem计算好的值
    body.removeChild(d);
    var k = document.getElementById('screenFix')?document.getElementById('screenFix').getAttribute('data-size'): 750;
    function setFont() {
        var realRem = fz;
        if(dw != fz){//不相等 则被缩放了
            realRem = fz * (fz / dw)
        }
        html.style.fontSize = html.clientWidth / k * realRem + "px";
    }
    setFont();
    document.addEventListener('DOMContentLoaded', setFont, false);
    window.addEventListener('resize', setFont, false);
    window.addEventListener('load', setFont, false);

    var isLand = window.orientation === undefined ? $(window).width() > $(window).height() : (window.orientation === 90 || window.orientation === -90);
    var isEditing = document.activeElement.tagName === 'INPUT';
    if (isLand && isEditing) $('input').blur();
}());