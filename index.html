<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="gbk" />
    <meta http-equiv="x-ua-compatible" content="IE=edge,chrome=1" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta content="yes" name="mobile-web-app-capable" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta name="author" content="Design:CP; Web Layout:CP;" />
    <meta name="robots" content="all" />
    <meta
      name="Description"
      content="王者营地，9月简单点，月初领礼券，9月30日抽大奖"
    />
    <meta name="Keywords" content="王者营地,月初领礼券,峡谷休闲对局" />
    <title>9月简单点</title>
    <link rel="stylesheet" href="ossweb-img/index.css" />
  </head>

  <body>
    <!-- rem 适配 Start -->
    <script
      id="screenFix"
      src="./js/remFix.js"
      data-size="750"
      title="size is design width"
    ></script>

    <h1 class="hide">9月简单点</h1>
    <div id="main">
      <div class="page-wrap">
        <div class="page">
          <a
            href="javascript:;"
            id="backBtn"
            class="btn-top btn-back pa bg t"
            onclick="easReport('return')"
            >返回</a
          >
          <a
            href="javascript:TGDialogS('pop-rule');"
            class="btn-top btn-rule pa bg t"
            onclick="easReport('rule')"
            >活动规则</a>
          <a href="javascript:TGDialogS('pop-address');" class="btn-top btn-rule pa bg t" onclick="easReport('rule')">活动规则</a>
                <a href="javascript:TGDialogS('pop-record');" class="btn-top btn-rule pa bg t" onclick="easReport('rule')">活动规则</a>
                <a href="javascript:TGDialogS('pop-win');" class="btn-top btn-rule pa bg t" onclick="easReport('rule')">活动规则</a>
                <a href="javascript:TGDialogS('pop-prize');" class="btn-top btn-rule pa bg t" onclick="easReport('rule')">活动规则</a>
          <a
            href="javascript:;"
            id="shareBtn"
            class="btn-top btn-share pa bg t"
            onclick="easReport('share')"
            >分享礼物</a>

          <div class="head">
            <!--- 登录  --->
            <div class="loginfo">
              <div id="login" class="loginfo-wrap">
                <div id="logined" class="loginfo-box" style="display: flex">
                  欢迎您，
                  <!-- <span class="user_name server_name">玩家大区</span>- -->
                  <!-- <span class="user_name role_name" id="roleInfo"></span> -->
                  <!-- <span class="user_name userName" id="areaName"></span> -->
                  <span class="user_name areaName" id="roleInfo"></span>
                  <a
                    id="changeRoleBtn"
                    class="btn-change changeRole"
                    href="javascript:;"
                    title="切换角色"
                    onclick="easReport('changeRule')"
                    >【切换角色】</a
                  >
                </div>
              </div>
            </div>
            <!---// 登录  --->
            <div class="time bg t">9.4-9.30</div>
          </div>

          <div class="page-con"></div>
        </div>
      </div>

      <!-- 弹窗 -->
      <!-- 活动规则 -->
      <div id="pop-rule" class="pop pop-rule">
        <a href="javascript:closeDialog();" class="pop-close bg t pa">关闭</a>
        <div class="pop-con">
          <div class="pop-tit t">活动规则</div>
          <div class="pop-body">
            <div class="rule-list scroll">
              <p>
                <em class="ind">【活动时间】</em>
                <br />9月29日00:00:00~10月1日17:00:00选择奖池兑换号码；10月1日18:00——10月1日23:59为开奖时间
              </p>

              <p>
                <em class="ind">【活动流程】</em>
                <br />玩家通过首次登录或参与活动任务获得票据，最多可获得3张
                <br />用户可选择把票据兑换到自己心意的奖池中，换取号码；
                <br />10月1日玩家登录活动，查看中奖情况
              </p>
              <p>
                <em class="ind">【活动入口】</em> <br />王者营地-我-每日福利
                <br />王者营地-游戏-每日福利 <br />王者荣耀-微社区
              </p>
              <p><em class="ind">【奖励详情】</em></p>
              <b>票据获得方式</b>
              <div class="table-wrap1 table-wrap">
                <table>
                  <thead>
                    <tr>
                      <th width="45%">获得方式</th>
                      <th width="25%">奖励名称</th>
                      <th width="15%">数量</th>
                      <th width="12%">概率</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>首次登录本活动</td>
                      <td>抽号次数+1</td>
                      <td>500w</td>
                      <td>必得</td>
                    </tr>
                    <tr>
                      <td>
                        9.29-10.1 17：00前完成<br />【邀请好友参与活动】任务
                      </td>
                      <td>抽号次数+1</td>
                      <td>500w</td>
                      <td>必得</td>
                    </tr>
                    <tr>
                      <td>9.29-10.1 17：00前预约<br />【订阅活动日历提醒】</td>
                      <td>抽号次数+1</td>
                      <td>500w</td>
                      <td>必得</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <p><em class="ind">【活动任务奖励获取】</em></p>
              <b>票据获得方式</b>
              <div class="table-wrap1 table-wrap">
                <table>
                  <thead>
                    <tr>
                      <th width="45%">获得方式</th>
                      <th width="25%">奖励名称</th>
                      <th width="15%">数量</th>
                      <th width="12%">概率</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>
                        9.29-10.1 17：00前完成<br />【邀请好友参与活动】任务
                      </td>
                      <td>亲密玫瑰*5</td>
                      <td>500w</td>
                      <td>必得</td>
                    </tr>
                    <tr>
                      <td>
                        9.29-10.1 17：00前预约<br />【订阅活动日历提醒】，<br />10.1登录活动领取
                      </td>
                      <td>“哟~”限时14天语音包</td>
                      <td>500w</td>
                      <td>必得</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <p><em class="ind">【票据兑换号码抽奖池】</em></p>
              <div class="table-wrap1 table-wrap">
                <table>
                  <thead>
                    <tr>
                      <th width="28%">奖池名称</th>
                      <th width="40%">奖励名称</th>
                      <th width="10%">总数量</th>
                      <th width="25%">概率</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>PS5奖池</td>
                      <td>PS5(轻薄版 1TB)光驱版/游戏机+白手柄套装1台*1</td>
                      <td>1</td>
                      <td>根据玩家参与人数，随机从中筛选1位玩家中奖</td>
                    </tr>
                    <tr>
                      <td>豪华周边大礼包</td>
                      <td>
                        12件餐具套装（Q萌出击）*1 奇妙积木-孙尚香-末日机甲手办*1
                        菱形咖啡杯-小乔款*1 小英雄系列软胶挂件-蔡文姬*1
                      </td>
                      <td>5</td>
                      <td>根据玩家参与人数，随机从中筛选1位玩家中奖</td>
                    </tr>
                    <tr>
                      <td>孙尚香-沉稳之力</td>
                      <td>孙尚香沉稳之力皮肤*1</td>
                      <td>10</td>
                      <td>根据玩家参与*人数，随机从中筛选1位玩家中奖</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <strong class="red"
                >将在参与对应奖池的玩家中随机抽取相应数量的中奖玩家，中奖结果将在2025年10月1日18点后在活动主页面公布。</strong
              >
              <p>
                <em class="ind">【奖励发放】</em>
                <br />1、1、游戏内道具奖励通过邮件发放，由于发放量较大，预计24小时内到账，当游戏内系统邮件数量达到上限后，无法再领取新道具，请注意及时
                清理，以免影响发货；
                <br />2、2、获得实物奖励的用户，请注意在活动截止前及时填写收货地址等相关信息，实物奖励将在活动结束后的60个工作日内邮寄至用户填写收货地址。若在活动截止后未完成收货信息填写，<b
                  class="red"
                  >因逾期/错填/漏填/快递派件失败导致的奖励发放失败视为放弃中奖资格，一律不予补发</b
                >。个人信息将严格保密仅用于作为发放活动奖励用途。奖励在本页面的“获奖记录”进行查询，若出现未到账情况请联系腾讯客服。
                ；
              </p>
              <p>
                <em class="ind">【其他说明】</em>
                <br />1、本活动的参与用户应为具有完全民事行为能力的自然人，且活动期间仅可以使用一个合法有权的有效账号（QQ号码、微信账号或手机号码）参与活动；
                <br />2、互联网运行存在诸多不确定性因素，如因不可抗力、网络、通讯线路故障、计算机大规模瘫痪及活动中存在大面积作弊行为等原因致使本活动出现异常情况或难以继续开展的，腾讯有权采取包括但不限于通过各种方式消除异常情况或调整、暂停、取消本活动等合理措施保障腾讯及用户的合法权益，您表示理解，由此给您造成的损失，您同意不追究腾讯的责任。
                <br />3、您应关注本活动规则有关时间的约定，如因非腾讯原因导致您在本规则约定时间内未参与活动、兑换奖品或领取奖品，则视为您放弃参与活动或放弃领奖，全部损失由您自行承担，腾讯不会给予您任何形式的补偿。
                <br />4、您应按照腾讯要求如实提供您联系地址等信息，如由于您提供的信息不真实不完整等导致奖品无法发放或发放错误等一切后果，由您自行承担。
                <br />5、您若有下列任何一种行为或情况的，活动发起方将有权取消您的参与资格、获奖资格等：
                <br />(1) 不符合参与资格的； <br />(2) 提供虚假信息的；
                <br />(3) 虚假交易或恶意破坏活动的； <br />(4)
                以任何机器人软件、蜘蛛软件、爬虫软件、刷奖软件或其它任何自动方式、不正当手段等参与本活动的；
                <br />(5) 有任何违反诚实信用、公序良俗、公平、公正等原则行为的；
                <br />(6) 其他违反相关法规、本规则行为的。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 恭喜中奖了 -->
      <div id="pop-win" class="pop pop-win">
        <a href="javascript:closeDialog();" class="pop-close bg t pa">关闭</a>
        <div class="pop-con">
          <p class="pop-title t">恭喜中奖了</p>
          <div class="pop-body">
            <p>
                <img src="./ossweb-img//awa-bg.png" alt="奖励图标">
                <span class="award-name">----</span>
            </p>
            <!-- 实物周边活动结束后的60个工作日内邮寄发放，请完整填写收件地址, 漏填错填将视为放弃奖励 -->
            <p>游戏道具奖励预计24小时发放至<b class="y">【xxx区服-角色 昵称】</b>邮箱内，注意查收</p>
          </p>
        </div>
        <div class="btns-box">
                  <a
                    href="javascript:commitPersonInfo();"
                    id="milo-submitPersonInfo"
                    class="btn-adr bg t"
                    onclick="easReport('submit')"
                    >提交</a
                  >
                </div>
      </div>

      <!-- 获奖记录 -->
      <div id="pop-record" class="pop pop-record">
        <a href="javascript:closeDialog();" class="pop-close bg t pa">关闭</a>
        <div class="pop-con">
          <div class="pop-tit t">获奖记录</div>
          <div class="pop-body">
            <div class="pop-desc">
              游戏道具预计24小时发放至游戏； <br />周边奖励活动结束后<b
                class="y"
                >60个工作日内</b
              >发放。
            </div>
            <!-- 默认无数据：  J-record添加类名 no-data -->
            <div class="pop-cnt scroll J-record">
              <div class="no-data-txt">暂未获得奖励</div>
              <ul class="record-list">
                <li>
                  <div class="record-img">
                    <img src="ossweb-img/prize/1.png" alt=" " />
                  </div>
                  <div class="record-info">
                    <div class="record-name">营地动效头像框</div>
                    <div class="record-time">
                      <span>2025-05-22 01:31:01</span>
                    </div>
                    <div class="record-adr"><span>微信37区-冥想繁荣</span></div>
                  </div>
                </li>
              </ul>
            </div>
            <div class="btns-box">
              <a
                href="javascript:;"
                class="btn-txdz bg t"
                onclick="easReport('btn-txdz')"
                style="display: none"
                >填写地址</a
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 填写地址 -->
      <div id="pop-address" class="pop pop-address">
        <a href="javascript:closeDialog();" class="pop-close bg t pa">关闭</a>
        <div class="pop-con">
          <div class="pop-tit t">收货信息</div>
          <div class="pop-body">
            <div class="pop-cnt">
              <div class="form">
                <div class="form_item">
                  <label for="milo-personName">姓名：</label>
                  <div class="input_wrap">
                    <input
                      id="milo-personName"
                      name="name"
                      type="text"
                      class="input milo-personName"
                      placeholder="点击输入姓名"
                    />
                  </div>
                </div>
                <div class="form_item">
                  <label for="milo-province">省份：</label>
                  <div class="input_wrap">
                    <select
                      name="pro"
                      id="milo-province"
                      class="input milo-province"
                    >
                      <option value="">请选择您所在的省</option>
                    </select>
                  </div>
                </div>
                <div class="form_item">
                  <label for="milo-province">城市：</label>
                  <div class="input_wrap">
                    <select name="pro" id="milo-city" class="input milo-city">
                      <option value="">请选择您所在的市</option>
                    </select>
                  </div>
                </div>
                <div class="form_item">
                  <label for="milo-province">区县：</label>
                  <div class="input_wrap">
                    <select
                      name="pro"
                      id="milo-region"
                      class="input milo-region"
                    >
                      <option value="">请选择您所在的区、县</option>
                    </select>
                  </div>
                </div>
                <div class="form_item">
                  <label for="milo-personAddress">地址：</label>
                  <div class="input_wrap">
                    <textarea
                      id="milo-personAddress"
                      name="sAddress"
                      class="input milo-personAddress"
                      placeholder="请输入您的详细地址"
                    ></textarea>
                  </div>
                </div>
                <div class="btns-box">
                  <a
                    href="javascript:commitPersonInfo();"
                    id="milo-submitPersonInfo"
                    class="btn-save bg t"
                    onclick="easReport('submit')"
                    >提交</a
                  >
                </div>
              </div>
            </div>

            <div class="pop-desc">
              实物周边活动结束后的60个工作日内邮寄发放，请完整填写收件地址，漏填错填将视为放弃奖励
            </div>
          </div>
        </div>
      </div>

      <!-- 恭喜获得 -->
      <div id="pop-prize" class="pop pop-prize">
        <a href="javascript:closeDialog();" class="pop-close bg t pa">关闭</a>
        <div class="pop-con">
          <div class="pop-tit t">恭喜您获得</div>
          <div class="pop-body">
            <div class="pop-cnt">
              <div class="pop-desc">
                游戏道具会在24小时内发放至游戏内邮箱，实物道具将在活动结束后的60个工作日内寄出，请准确填写收货信息~
              </div>
              <!-- 默认展示1个奖励；   3个奖励：prize-list 添加类名 .item-3 -->
              <ul class="prize-list">
                <li>
                  <div class="prize-img">
                    <img src="ossweb-img/prize/1.png" alt=" " />
                  </div>
                  <div class="name-box">
                    <div class="prize-img-name">营地动效头像框</div>
                  </div>
                </li>
                <li style="display: none">
                  <div class="prize-img">
                    <img src="ossweb-img/prize/1.png" alt=" " />
                  </div>
                  <div class="name-box">
                    <div class="prize-img-name">五谷丰登营地动态头像框x1</div>
                  </div>
                </li>
                <li style="display: none">
                  <div class="prize-img">
                    <img src="ossweb-img/prize/2.png" alt=" " />
                  </div>
                  <div class="name-box">
                    <div class="prize-img-name">人生营家亲密道具x5</div>
                  </div>
                </li>
              </ul>
              <!-- <ul class="prize-list item-3" style="display: none">
                            <li>
                                <div class="prize-img"><img src="ossweb-img/prize/6.png" alt=" "></div>
                                <div class="name-box">
                                    <div class="prize-img-name">小英雄系列软胶挂件-杨玉环款</div>
                                </div>
                            </li>
                            <li>
                                <div class="prize-img"><img src="ossweb-img/prize/6.png" alt=" "></div>
                                <div class="name-box">
                                    <div class="prize-img-name">小英雄系列软胶挂件-杨玉环款</div>
                                </div>
                            </li>
                            <li>
                                <div class="prize-img"><img src="ossweb-img/prize/6.png" alt=" "></div>
                                <div class="name-box">
                                    <div class="prize-img-name">小英雄系列软胶挂件-杨玉环款</div>
                                </div>
                            </li>
                        </ul> -->
            </div>
            <div class="btns-box">
              <a
                href="javascript:;"
                class="btn-txdz bg t"
                onclick="easReport('btn-txdz')"
                style="display: none"
                >填写地址</a
              >
              <a
                href="javascript:closeDialog();"
                class="btn-quer bg t"
                onclick="easReport('btn-quer')"
                >确认</a
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 票据不足 -->

      <!-- 邀请好友 -->

      <!-- 助力好友 -->

      <!-- 通用提示弹窗 -->
      <div id="popAlert" class="pop pop-com">
        <a href="javascript:closeDialog();" class="pop-close bg t pa">关闭</a>
        <div class="pop-con">
          <div class="pop-tit t">温馨提示</div>
          <div class="pop-body">
            <div class="pop-cnt">
              <div class="pop_tip_wrap J_alert_msg">
                <span class="com-text"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script
      type="text/javascript"
      src="//ossweb-img.qq.com/images/js/jquery/jquery-1.11.3.min.js"
    ></script>
    <script
      type="text/javascript"
      src="//game.gtimg.cn/images/js/2018foot/foot.js"
    ></script>
    <script
      type="text/javascript"
      src="//ossweb-img.qq.com/images/js/milo_bundle/milo.js"
    ></script>
    <script
      type="text/javascript"
      src="//game.gtimg.cn/images/js/milo-next/latest/milo.gbk.min.js"
    ></script>
    <script
      type="text/javascript"
      src="//game.gtimg.cn/images/js/su/danMu.beta.min.js"
    ></script>

    <!-- 弹窗逻辑js -->
    <script
      type="text/javascript"
      charset="UTF-8"
      src="./js/common.js"
    ></script>
    <script type="text/javascript" charset="UTF-8" src="./js/lay.js"></script>

    <script
      src="//gameact.qq.com/comm-htdocs/js/game_area/yxzj_server_select.js"
      charset="gbk"
    ></script>
    <script src="//camp.qq.com/h5/webdist/sdk/camp-launch-app/camp-launch-app.1.12.0.min.js"></script>
    <script src="//camp.qq.com/h5/webdist/sdk/camp-jsbridge/camp-jsbridge.latest.min.js"></script>
    <script src="//camp.qq.com/h5/webdist/sdk/camp-api/index.umd.min.js"></script>

    <script>
      const { launchCamp, launchApp } = window.Camp && window.Camp;
      // Milo.showVConsole();

      try {
        (window.GameHelper || window.JsCommonApi).h5LoadFinish();
      } catch (e) {}

      function goPrev() {
        if (
          window.CampJsBridge &&
          typeof window.CampJsBridge.closeActivity === "function"
        ) {
          window.CampJsBridge.closeActivity();
        } else {
          console.error("CampJsBridge load failed");
        }
      }
    </script>
    <script
      type="text/javascript"
      src="//ossweb-img.qq.com/images/js/eas/eas.js"
    ></script>
    <script title="eas上报">
      const globalData = { openid: "", ext1: "", ext2: "" }; // todo:当有登录台以后，更新 globalData.openid
      var shareCode = "";
      function easReport(action) {
        try {
          EAS.SendClick({
            e_c: "pvp.a20250901simple." + action, //   a20250901simple 更新当前包名
            openid: globalData.openid,
            ext1: globalData.ext1,
            ext2: globalData.ext2,
          });
        } catch (e) {
          console.log(e);
        }
      }
    </script>

    <script title="页面逻辑">
      //showAlert('通用提示内容，自定义内容')

      //道具对应
      // var giftName = [
      //     { name: '营地动效头像框', img: 'prize/1.png' },
      //     { name: '人生营家亲密道具*5', img: 'prize/2.png' },
      //     { name: 'PS5（轻薄版 1TB)光驱版', img: 'prize/3.png' },
      //     { name: '亲密转区卡', img: 'prize/4.png' },
      //     { name: '乐开花系列冰箱贴-瑶款', img: 'prize/5.png' },
      //     { name: '小英雄系列软胶挂件-杨玉环款', img: 'prize/6.png' },
      //     { name: '李元芳-逐浪之夏', img: 'prize/7.png' },
      //     { name: '营地币*1', img: 'prize/8.png' }
      // ]
    </script>
  </body>
</html>
